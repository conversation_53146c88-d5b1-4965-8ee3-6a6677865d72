<template>
  <common-dialog ref="dialog" width="60%" title="订单详情" class="blueDialogScroll">
    <el-form class="forms"  ref="form" size="small" label-width="110px">
        <div class="area-title">订单信息</div>
        <el-form-item label="订单编号:">
          <div class="flex">
              <div style="display: flex;align-items: center;">
                <div style="margin-right: 20px">{{ info.orderSn }}</div>
                <el-tag type="success">{{ info.orderStatusText }}</el-tag>
              </div>
          </div>
        </el-form-item>
        <el-form-item label="订单类别:">
          {{ info.orderCategoryText }}
        </el-form-item>
        <el-form-item label="订单来源:" >
          {{ info.orderSourceText }}
        </el-form-item>
        <el-form-item label="下单时间:" >
          {{ info.addTime }}
        </el-form-item>
        <el-form-item label="订单完成时间:" >
          {{ info.confirmTime }}
        </el-form-item>
        <el-form-item label="订单总金额:" >
          {{ info.totalAmount }}
        </el-form-item>
        <el-form-item label="优惠金额:" >
          {{ info.discountAmount }}
        </el-form-item>
        <el-form-item label="实付金额:">
          {{ info.actualAmount }}
        </el-form-item>
        <div class="area-title">购票信息</div>
        <el-table :data="info.orderAdmission"  >
          <el-table-column label="序号" align="center" type="index" />
          <el-table-column label="门票名称" align="center" prop="admissionName" :show-overflow-tooltip="true" />
          <el-table-column label="门票数量" align="center" prop="buyNum" :show-overflow-tooltip="true"/>
          <el-table-column label="门票价格" align="center" prop="admissionPrice" :show-overflow-tooltip="true" />
        </el-table>
        <div class="area-title">门票信息</div>
          <el-table :data="info.orderAdmissionDetail">
            <el-table-column label="序号" align="center" type="index" />
            <el-table-column label="游客姓名" align="center" prop="contactsName" :show-overflow-tooltip="true" />
            <el-table-column label="手机号" align="center" prop="contactsPhone" :show-overflow-tooltip="true"/>
            <el-table-column label="证件类型" align="center" prop="contactsDocumentType" :show-overflow-tooltip="true" >
              <template slot-scope="scope">
                  {{ scope.row.contactsDocumentType | contactsDocumentType}}
              </template>
            </el-table-column>
            <el-table-column label="证件号码" align="center" prop="contactsCredentialNo" :show-overflow-tooltip="true" />
            <el-table-column label="核销状态" align="center" prop="statusText" :show-overflow-tooltip="true" />
            <el-table-column label="核销时间" align="center" prop="verifincationTime" :show-overflow-tooltip="true" />
            <el-table-column label="门票日期" align="center" prop="admissionDate" :show-overflow-tooltip="true" />
            <el-table-column label="核销码" align="center" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                 <el-button
                  size="mini"
                  type="text"
                  @click="viewTickets(scope.row)"
                >查看门票</el-button>
              </template>
            </el-table-column>
          </el-table>
        <div class="area-title">支付信息</div>
        <el-form-item label="支付时间:">
          {{ info.payTime }}
        </el-form-item>
        <el-form-item label="支付方式:">
          {{ info.payType | payType}}
        </el-form-item>
        <el-form-item label="交易单号:">
          {{ info.tradeSn }}
        </el-form-item>
        <el-form-item label="商户单号:">
          {{ info.businessSn }}
        </el-form-item>
        <el-form-item label="交易金额:" >
          {{info.actualAmount}}元
        </el-form-item>
        <div v-if="info.refundDetail">
          <div class="area-title">退款记录</div>
            <el-form-item label="退款单号:">
            {{ info.refundDetail.refundSn || '' }}
          </el-form-item>
          <el-form-item label="退款申请时间:" >
            {{ info.refundDetail.refundApplyTime || ''  }}
          </el-form-item>
          <el-form-item label="退款完成时间:" >
            {{ info.refundDetail.refundCompleteTime || ''  }}
          </el-form-item>
          <el-form-item label="退款申请人:">
            {{ info.refundDetail.refundApplyContactsName || ''  }}
          </el-form-item>
          <el-form-item label="退款金额:">
            {{ info.refundDetail.refundAmount || ''  }}元
          </el-form-item>
          <el-form-item label="退款方式:">
            {{ info.refundDetail.refundType |  refundType}}
          </el-form-item>
          <el-form-item label="退款状态:">
            {{ info.refundDetail.refundStatus | refundStatus}}
          </el-form-item>
          <el-form-item label="退款原因:">
            {{ info.refundDetail.refundReason || ''  }}
          </el-form-item>
          <el-form-item label="退款说明:">
            {{ info.refundDetail.refundExplain || ''  }}
          </el-form-item>
        </div>
        <div class="area-title">操作记录</div>
        <el-table :data="info.orderRefundProgressList">
          <el-table-column label="序号" align="center" type="index" />
          <el-table-column label="操作时间" align="center" prop="addTime" :show-overflow-tooltip="true" />
          <el-table-column label="操作人" align="center" prop="operateUser" :show-overflow-tooltip="true"/>
          <el-table-column label="操作详情" align="center" prop="refundProgressName" :show-overflow-tooltip="true" />
        </el-table>
      <el-form-item class="form-footer">
        <el-button type="primary" @click="refund(info.orderSn)" v-if="info.orderStatusText=='待使用'||info.orderStatusText=='已完成'">客诉退款</el-button>
        <el-button @click="quxiao">返回</el-button>
      </el-form-item>
    </el-form>
    <refundDialog ref="refunds" @success="onRefundSuccess" />
    <QRCodes ref="qRCodes" />
  </common-dialog>
</template>

<script>
import { orderDetail } from "@/api/order/index";
import refundDialog from "./refundDialog.vue"
import QRCodes from "./qRCode.vue"
export default {
  name: 'orderDetails',
  components: {
    refundDialog,
    QRCodes
  },
  data() {
    return {
      disabled:false,
      disabled2:false,
      loading:false,
      text:'',
      info:{
        // orderAdmissionDetail:{},
        refundDetail:{},
        orderAdmission:[],
        orderAdmissionDetail:[],
        orderRefundProgressList:[]
      },
    }
  },
  filters:{
    refundType(v){
      let str = ''
      if(v==1){
        str = '微信原路退回'
      }else{
        str = ''
      }
      return str
    },
    contactsDocumentType(v){
      let str = ''
      if(v==1){
        str = '身份证'
      }
      return str
    },
    payType(v){
      let str = ''
      if(v==0){
        str = '未知（第三方平台）'
      }
      if(v==1){
        str = '微信支付'
      }
      if(v==2){
        str = '翼支付线下（收银台）'
      }
      if(v==3){
        str = '翼支付线上（小程序）'
      }
      return str
    },
    refundStatus(v){
      let str = ''
      if(v==300){
        str = '退款中'
      }else if(v==301){
        str = '已退款'
      }else if(v==302){
        str = '退款驳回'
      }
      return str
    }
  },
  mounted() {

  },
  methods: {
    //客诉退款
    refund(orderSn){
      this.$refs.refunds.showDialog(orderSn);
    },
    //查看门票
    viewTickets(val){
      if (!val.verifincationSn) {
        this.$message.error('暂无核销码')
        return
      }
      this.$refs.qRCodes.showDialog(val.verifincationMA,'orderDetails')
    },
    //返回
    quxiao(){
      this.$refs.dialog.dialogVisible = false
    },
    showDialog(v) {
      let params = {
        orderSn:v
      }
      orderDetail(params).then(res=>{
        this.info = res.data
      })
      this.$refs.dialog.dialogVisible = true
    },
    onRefundSuccess() {
      this.$emit("getList")
      this.quxiao()
    }
  }
}
</script>

<style lang="scss" scoped>
//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  height: 700px !important;
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}
.flex {
  display: flex;
  justify-content: space-between;
}
//下载按钮
::v-deep .flex .el-button--primary{
    background-color: #1890ff !important;
    border-color: #1890ff !important;
  }

</style>
