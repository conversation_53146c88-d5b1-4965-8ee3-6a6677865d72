<script>
import {getToken} from "@/utils/auth";
import {bannerConfig, bannerDetails} from "@/api/indexAndBanner/index";

export default {
  name: "addDialog",
  data() {
    return {
      imgUrl: process.env.VUE_APP_FILE_URL,
      visible: false,
      form: {
        fileUrl: '',//相关链接
        isWork: 2,
        name: '',
        price: '',
        period: '',
        refund: '',
        num: '',
        whts: [
          // { imgList: [], linkUrl: '' },
        ],
        szzttp: [],
        szztsp: {},
        sy: {}
      },
      uploadIndex: null,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/museum-main/oss/upload",
      rules: {
        fileRoute: [{required: true, message: '请输入相关链接'}],
      },

      srcList: ['https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      ],
      loading: false,
      isImage: '',//是图片还是视频

    };

  },
  created() {
    this.getList();
  },
  methods: {
    // 清空链接
    deleteClick(index){
      if(this.form.szzttp[index].fileRoute){
        this.form.szzttp[index].fileRoute=''
      }

    },
    // 获取列表
    getList() {
      bannerDetails().then(res => {
        // this.form = res.data;
        /*for (let key in this.form) {
          if (res.data.hasOwnProperty(key)) {
            this.$set(this.form, key, res.data[key])
          }
        }*/
        this.form.szzttp = res.data.szzttp || [];
        this.form.szztsp = res.data.szztsp || {};
        this.form.sy = res.data.sy || {};
        this.form.whts = res.data.whts || [];
      });
    },
    open() {
      this.visible = true;
    },
    onExceed() {
      this.$message.error('超出最大限制数量，请重新选择！');
      /*  if (value == 1) {
            this.$message.error('超出最大限制数量，请重新选择！');
        } else if (value == 2) {
            this.$message.error('超出最大限制数量，请重新选择！');
        } else if (value == 3) {
            this.$message.error('超出最大限制数量，请重新选择！');
        } else if (value == 4) {
            this.$message.error('超出最大限制数量，请重新选择！');
        }*/
    },
    // 上传失败
    handleAvatarError(res) {
      console.log(res, './123123');
    },
    handleAvatarSuccess(res, value, num) {
      console.log(res, './上传data');
      console.log(value, './上传参数');
      console.log(num, '.方法传参');
      const fileInfo = {fileUrl: res.data};
      if (num == 1) {
        this.form.szzttp.push(fileInfo);
      } else if (num == 2) {
        this.$set(this.form.whts, this.uploadIndex, fileInfo);
      } else if (num == 3) {
        this.form.sy = {
          fileType: this.form.sy.fileType,
          fileUrl: fileInfo.fileUrl,
        };
      } else {
        this.form.szztsp = fileInfo;
      }
    },
    beforeAvatarUpload(file, value, num, index) {
      console.log(file, './上传视频');
      if (index !== undefined) {
        this.uploadIndex = index;
      }
      if (num == 1) {
        if (this.form.szzttp.length === 12) {
          this.$message.error('最多上传12张图片');
          return false;
        }
        const isLt2M = file.size / 1024 / 1024 < 5;

        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 5 MB!');
        }
        return isLt2M;
      } else if (num == 2) {
        if (this.form.whts.length === 1 && this.form.whts[0].fileUrl != '') {
          this.$message.error('最多上传1张图片');
          return false;
        }
        const isLt2M = file.size / 1024 / 1024 < 5;

        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 5 MB!');
        }
        return isLt2M;
      } else if (num == 3) {
        if (file.type.substring(0, 5) == 'image') {
          this.form.sy.fileType = '1';
          const isLt2M = file.size / 1024 / 1024 < 5;
          if (!isLt2M) {
            this.$message.error('上传图片大小不能超过 5 MB!');
          }
          return isLt2M;
        } else {
          this.form.sy.fileType = '2';
          const isLt2M = file.size / 1024 / 1024 < 100;
          if (!isLt2M) {
            this.$message.error('上传视频大小不能超过 100 MB!');
          }
          return isLt2M;
        }
      } else {
        const isLt2M = file.size / 1024 / 1024 < 100;
        if (!isLt2M) {
          this.$message.error('上传视频大小不能超过 100 MB!');
        }
        return isLt2M;

      }
    },

    delPic(value, index, url) {
      if (value == 1) {
        this.form.szzttp.splice(index, 1);
      } else if (value == 2) {
        this.form.szztsp = {};
      } else if (value == 3) {
        this.form.whts[index].fileUrl = '';
      } else if (value == 4) {
        this.form.sy = {};
      } else if (value == 5) {
        this.form.whts.splice(index, 1);
      }
    },
    // 添加文化banner图
    addLine() {
      let obj = {
        fileUrl: ''
      };
      if (this.form.whts.length < 5) {
        this.form.whts.push(obj);
      } else {
        this.$message.error('最大允许上传数量为5');
      }
      console.log(this.form.whts, './1231231');
    },
    // 保存表单
    saveForm(form) {
      let params = {
        szztTpList: this.form.szzttp,//数字展厅图片
        szztSp: this.form.szztsp,//数字展厅视频
        whtsList: this.form.whts,//文化探索列表
        syList: this.form.sy,//首页列表
      };
      console.log(params);
      // return
      this.$refs[form].validate(async (valid) => {
        console.log(valid);
        // return
        if (valid) {
          this.loading = true;
          try {
            const res = await bannerConfig(params);
            const {code} = res;
            if (code === 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              });
              this.$emit('success');
              this.loading = false;
              this.$refs.dialog.dialogVisible = false;
            }
          } catch {
            this.loading = false;
          }
        } else {
          return false;
        }
      });
    },
  }
}
</script>

<template>
  <div class="content">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <div class="content-body">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <div class="left-icon"></div>
            <span>首页和Banner图维护</span>
          </div>
          <div class="card-body">
            <div>
              <div class="second-title">数字展厅Banner图</div>
              <el-card class="item-card">
                <!--                            <el-form ref="form" :model="form" :rules="rules" label-width="120px">-->
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="上传图片" prop="indeximg">
                      <div class="img-box">
                        <el-upload ref="productImgRef" v-show="form.szzttp.length < 12"
                                   class="avatar-uploader" :headers="headers" :action="uploadUrl"
                                   :show-file-list="false" multiple :limit="12" accept=".jpg,.png"
                                   :on-success="function(res,value){return handleAvatarSuccess(res,value,'1')}"
                                   :before-upload="function(res,value){return beforeAvatarUpload(res,value,'1')}"
                                   :on-exceed="function(res,value){return onExceed(res,value,'1')}">
                          <!--              <img v-if="form.logo" :src="form.logo" class="avatar">-->
                          <i class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>

                        <div v-for="(url, index) in form.szzttp" :key="index"
                             class="img-item-container">
                          <el-image style="width: 128px; height: 128px"
                                    :src="imgUrl + url.fileUrl"
                                    fit="cover"></el-image>
                          <div class="operation-btns">
                            <span @click="delPic('1',index)">删除</span>
                          </div>
                        </div>
                      </div>
                      <div style="color:#8c939d">
                        最多允许上传12张图片，图片小于5MB，支持扩展名:jpg，png
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="相关链接"
                                  :rules="{required: true, message: '请输入相关链接' }" label-width="200px">
                      <div class="img-box" style="margin-bottom: 5px" v-for="(item, index) in form.szzttp" :key="'url'+index">
                        <el-input v-model="item.fileRoute" placeholder='请输入'></el-input>
                        <el-button type="text" @click="deleteClick(index)">清空</el-button>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="上传视频">
                      <div class="img-box">
                        <el-upload ref="productImgRef" class="avatar-uploader"
                                   v-if="!form.szztsp.fileUrl" :headers="headers" :action="uploadUrl"
                                   :show-file-list="false" accept=".mp4" multiple :limit="1"
                                   :on-success="handleAvatarSuccess" :on-error="handleAvatarError"
                                   :before-upload="beforeAvatarUpload"
                                   :on-exceed="function(res){return onExceed(res,'4')}">
                          <!--              <img v-if="form.logo" :src="form.logo" class="avatar">-->
                          <i class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>

                        <div class="img-item-container" v-else>
                          <video
                            :src="imgUrl + form.szztsp.fileUrl"
                            style="width: 128px; height: 128px"></video>
                          <div class="operation-btns">
                            <span @click="delPic('2')">删除</span>
                          </div>
                        </div>
                      </div>
                      <div style="color:#8c939d">
                        仅允许上传一条视频文件，文件小于50M，视频格式:MP4
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!--                            </el-form>-->
              </el-card>
            </div>

            <div>
              <div class="title-btn">
                <div class="second-title">文化探索Banner图</div>
                <el-button class="edit_btn" @click="addLine" type="text">添加</el-button>
              </div>
              <!--                        <el-form ref="whtsForm" :model="form" :rules="rules" label-width="120px">-->
              <el-card v-for="(url,index) in form.whts" :key="index" class="item-card">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="上传图片" :prop="'whts.' + index + '.fileUrl'"
                                  :rules="{required: true, message: '请上传图片' }">
                      <div class=" img-box">
                        <el-upload ref="productImgRef" v-if="!url.fileUrl"
                                   class="avatar-uploader" :headers="headers" :action="uploadUrl"
                                   :show-file-list="false" multiple :limit="1" accept=".jpg,.png"
                                   :on-success="function(res,value){return handleAvatarSuccess(res,value,'2')}"
                                   :before-upload="function(res,value){return beforeAvatarUpload(res,value,'2', index)}"
                                   :on-exceed="function(res,value){return onExceed(res,value,'2')}">
                          <!--              <img v-if="form.logo" :src="form.logo" class="avatar">-->
                          <i class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <div v-else class="img-item-container">
                          <el-image style="width: 128px; height: 128px"
                                    :src="imgUrl + url.fileUrl"
                                    fit="cover"></el-image>
                          <div class="operation-btns">
                            <span @click="delPic('3',index,url)">删除</span>
                          </div>
                        </div>
                        <div style="color:#8c939d">
                          仅允许上传1张图片，图片小于5MB，支持扩展名:jpg,png
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="相关链接" :prop="'whts.' + index + '.fileRoute'"
                                  :rules="{required: true, message: '请输入相关链接' }">
                      <el-input v-model="url.fileRoute" placeholder='请输入'></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button class="edit_btn delete" @click="delPic('5', index)"
                           type="text">删除
                </el-button>
              </el-card>
              <!--                        </el-form>-->
            </div>

            <div>
              <div class="second-title">首页</div>
              <el-card class="item-card" style="margin-bottom:50px">
                <!--                            <el-form ref="form" :model="form" :rules="rules" label-width="120px">-->
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="上传图片/视频" prop="img">
                      <div class="img-box">
                        <el-upload ref="productImgRef" v-if="!form.sy.fileUrl"
                                   class="avatar-uploader" :headers="headers" :action="uploadUrl"
                                   :show-file-list="false" multiple :limit="1" accept=".jpg,.png,.mp4"
                                   :on-success="function(res,value){return handleAvatarSuccess(res,value,'3')}"
                                   :before-upload="function(res,value){return beforeAvatarUpload(res,value,'3')}"
                                   :on-exceed="function(res,value){return onExceed(res,value,'3')}">
                          <i class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <div v-else class="img-item-container">
                          <el-image v-if="form.sy.fileType==1"
                                    style="width: 128px; height: 128px"
                                    :src="imgUrl + form.sy.fileUrl"
                                    fit="cover"></el-image>
                          <video :src="imgUrl + form.sy.fileUrl"
                                 v-else-if="form.sy.fileType==2"
                                 style="width: 128px; height: 128px"></video>
                          <div class="operation-btns">
                            <span @click="delPic('4')">删除</span>
                          </div>
                        </div>
                      </div>
                      <div style="color:#8c939d">
                        仅允许上传1张图片或视频，图片小于5MB，视频小于100M，支持扩展名:jpg,png,mp4
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="相关链接" prop="sy.fileRoute">
                      <el-input v-model="form.sy.fileRoute" placeholder='请输入'></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!--  </el-form>-->
              </el-card>
            </div>
          </div>
          <el-button class="bottom-btn" type="primary" size="small" @click="saveForm('form')">保存</el-button>
        </el-card>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
::v-deep .avatar {
  width: 100px;
  height: 100px;
  display: block;
}

::v-deep .el-select {
  width: 100% !important;
}

::v-deep .el-card {
  border-radius: 10px;
  border: none;
}

.bottom-btn {
  display: flex;
  justify-content: center;
}

//上传图片
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

.img-item-container {
  border-radius: 10px;
  overflow: hidden;
  width: 128px;
  height: 128px;
  flex-shrink: 0;
  border: 1px solid #ccc;
  position: relative;

  .operation-btns {
    position: absolute;
    width: 100%;
    padding: 5px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
    left: 0;
    z-index: 99;
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
    line-height: 20px;
    font-size: 12px;
    gap: 10px;

    span {
      cursor: pointer;
    }
  }
}

.img-box {
  // display: flex;
  gap: 10px;
  display: flex;
  flex: 1;
  overflow-x: auto;
}

.content-body {
  margin: 15px;

  .second-title {
    margin-left: 20px;
    margin-bottom: 20px;
    margin-right: 10px;
  }

  .box-card {
    display: flex;
    flex-direction: column;
    position: relative;

    .bottom-btn {
      position: absolute;
      bottom: 10px;
      left: 50%;
      right: 50%;
      width: 60px;
    }

    .clearfix {
      display: flex;
      align-items: center;

      span {
        font-weight: bold !important;
      }

      .left-icon {
        width: 3px;
        height: 16px;
        margin-right: 4px;
        margin-top: 2px;
        border-radius: 10px;
        background-color: rgb(0, 121, 254);
      }
    }
  }

  .title-btn {
    display: flex;
    align-items: flex-start;
    line-height: 34px;
  }

  .item-card {
    margin-bottom: 10px;
    margin-left: 20px;
    margin-right: 20px;
    position: relative;

    .delete {
      position: absolute;
      top: 0;
      right: 10px;
    }
  }
}
</style>
