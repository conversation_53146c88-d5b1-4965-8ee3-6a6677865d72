<script>
import { getManageData } from "@/api/ticketPriceReview";
export default {
    name: "index",
    data () {
        return {
            showSearch: true,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                condition: ''
            },
            tableData: [],
            total: 0,
            loading: false
        };
    },
    mounted () {
        this.getList();
    },
    methods: {
        // 查看详情
        goDetails (row) {
            let query = {
                item: JSON.stringify(row),
              from: 'manage'
            };
            console.log(query, '.传参abc');
            this.$router.push({ path: 'ticketDetails', query: query });
        },
        getList () {
            getManageData(this.queryParams).then(res => {
                this.tableData = res.rows;
                this.total = res.total;
                // this.loading = false;
            }
            );
        },
        handleQuery () {
            this.getList();
        },
        /** 重置 */
        resetQuery () {
            for (var key in this.queryParams) {
                this.queryParams[key] = '';
            }
            this.queryParams.pageNum = 1;
            this.queryParams.pageSize = 10;
            this.getList();
        },
        onAdd () {
            this.$refs.addDialog.open();
        },
    }
}
</script>

<template>
    <div class="app-container">
        <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="关键字" prop="type">
                <el-input v-model="queryParams.condition" placeholder="请输入提交人、展厅名称、门票名称" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
            <el-table-column prop="showroomName" label="展厅名称"></el-table-column>
            <el-table-column prop="admissionName" label="门票名称"></el-table-column>
            <el-table-column prop="price" label="门票价格"></el-table-column>
            <el-table-column prop="serviceLifeName" label="使用权限"></el-table-column>
            <el-table-column prop="refundRulesName" label="退款规则"></el-table-column>
            <el-table-column prop="admissionRemark" label="门票备注"></el-table-column>
            <el-table-column prop="createByName" label="提交人"></el-table-column>
            <el-table-column prop="createTime" label="提交时间"></el-table-column>
            <el-table-column prop="name" label="操作" width="170">
                <template #default="{row}">
                    <el-button @click="goDetails(row)" size="mini" type="text" icon="el-icon-view">审核</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-input--small .el-input__inner {
    width: 280px;
}
</style>
