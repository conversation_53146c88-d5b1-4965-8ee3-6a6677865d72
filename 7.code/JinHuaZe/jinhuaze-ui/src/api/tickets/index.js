import request from '@/utils/request'

// 列表
export function getTicketPriceList(params) {
  return request({
    url: '/museum-main/admission/list',
    method: 'get',
    params
  })
}
export function getDigitalExhibitionHallDict() {
  return request({
    url: '/museum-main/digitalShowroomManage/list',
    method: 'get'
  })
}

export function updateTicketPrice(data, isEdit) {
  return request({
    url: `/museum-main/admission/${!isEdit ? 'save' : 'update'}`,
    method: 'post',
    data
  })
}
export function delTicketPrice(data) {
  return request({
    url: '/museum-main/admission/delete',
    method: 'post',
    data
  })
}
/**
 * 门票日期维护-保存
 */
export function save(data) {
  return request({
    url: '/museum-main/admission-filterate-date/save',
    method: 'post',
    data
  })
}
export function getList() {
  return request({
    url: '/museum-main/admission-filterate-date/admissionInfo',
    method: 'get'
  })
}
// 生成工作票-列表
export function orderList(params) {
  return request({
    url: '/museum-main/order-work/list',
    method: 'get',
    params
  })
}
//新增工作票
export function orderSave(data) {
  return request({
    url: '/museum-main/order-work/sava',
    method: 'post',
    data
  })
}
//下载工作票
export const importExcelZip = (data) => {
  return request({
    url: '/museum-main/order-work/importExcelZip',
    method: 'post',
    responseType: "blob",
    data
  });
}
// museum-main/order-work/details
export function workTicketDetails(params) {
  return request({
    url: '/museum-main/order-work/details',
    method: 'get',
    params
  })
}
//门票日期维护
export function getDateList(params) {
  return request({
    url: '/museum-main/admission-date-list/list',
    method: 'get',
    params
  })
}

//新增工作票
export function updateTicketDate(data) {
  return request({
    url: '/museum-main/admission-date-list/save',
    method: 'post',
    data
  })
}
//修改状态

export function updateDate(data) {
  return request({
    url: '/museum-main/admission-date-list/update',
    method: 'post',
    data
  })
}
