import request from '@/utils/request'

// 列表
export function getList(params, isManage) {
  return request({
    url: `museum-app/order/web/${isManage==1 ? 'webReviewRefundListForJingli' : 'webReviewRefundListForYunying'}`,
    method: 'get',
    params
  })
}

//museum-app/order/web/webReviewRefundForYunying
export function handleRefundCheck(data, isManage) {
  return request({
    url: `museum-app/order/web/${isManage==1 ? 'webReviewRefundForJingli' : 'webReviewRefundForYunying'}`,
    method: 'post',
    data
  })
}
