<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="关键字">
        <el-input v-model="queryParams.keyWord" placeholder="关键字" clearable style="width: 240px;" />
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker v-model="times" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="timeValue">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分类">
        <el-select v-model="queryParams.type" style="width: 100%" placeholder="分类" clearable>
          <el-option v-for="item in typeList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" size="mini" @click="add">新增内容</el-button>
      </el-form-item>
    </el-form>
    <el-table ref="tables" v-loading="loading" :data="list">
      <el-table-column label="文章标题" align="center" prop="articleTitle" :show-overflow-tooltip="true" />
      <el-table-column label="分类" align="center" prop="type" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{ scope.row.type | type }}
        </template>
      </el-table-column>
      <el-table-column label="精选推荐" align="center" prop="recommend" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{ scope.row.recommend | recommend }}
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="createTime" :show-overflow-tooltip="true" />
      <el-table-column label="发布人" align="center" prop="createPeople" :show-overflow-tooltip="true" />
      <el-table-column label="浏览量" align="center" prop="pageView" width="130" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="edit(scope.row.id)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-view" @click="details(scope.row.id)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <AddExploration ref="add" @success="getList" />
    <EditExploration ref="edit" @success="getList" />
    <DetailExploration ref="details" />
  </div>
</template>
<script>
import { list, del } from "@/api/culExploration/index";
import AddExploration from "./components/addExploration.vue";
import EditExploration from "./components/editExploration.vue";
import DetailExploration from "./components/detailExploration.vue";
export default {
  name: "exploration",
  dicts: [],
  components: {
    AddExploration,
    EditExploration,
    DetailExploration,
  },
  data() {
    return {
      typeList: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "文化探索",
          value: "1",
        },
        {
          label: "攻略",
          value: "2",
        },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyWord: "",
        startTime: "",
        endTime: "",
        type: "",
      },
      times: [],
      limit: 0,

      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  filters: {
    type(v) {
      let str = "";
      if (v == "1") {
        str = "文化探索";
      } else if (v == "2") {
        str = "攻略";
      }
      return str;
    },
    recommend(v) {
      let str = "";
      if (v == "0") {
        str = "否";
      } else if (v == "1") {
        str = "是";
      }
      return str;
    },
  },
  created() {
    this.getList();
  },
  methods: {
    timeValue(v) {
      this.queryParams.startTime = v[0];
      this.queryParams.endTime = v[1];
    },
    edit(id) {
      this.$refs.edit.showDialog(id);
    },
    details(id) {
      this.$refs.details.showDialog(id);
    },
    del(id) {
      this.$confirm("确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          del({ ids: id }).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    add() {
      this.$refs.add.showDialog();
    },
    async getList() {
      let that = this;
      try {
        const res = await list(that.queryParams);
        const { code, rows, total } = res;
        if (code === 200) {
          that.list = rows;
          that.total = total;
        } else {
          that.list = [];
        }
        that.loading = false;
      } catch {}
    },
    /** 搜索 */
    handleQuery() {
      this.getList();
    },
    /** 重置 */
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.queryParams.keyWord = "";
      this.times = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.queryParams.type = "";
      this.getList();
    },
  },
};
</script>

