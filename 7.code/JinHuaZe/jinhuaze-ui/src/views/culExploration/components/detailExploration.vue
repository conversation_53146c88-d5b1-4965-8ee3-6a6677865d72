<template>
  <common-dialog ref="dialog" width="61%" title="详情" class="blueDialogScroll" >
    <el-form  ref="form" size="small" >
      
        
      <h2 class="title">{{ formValues.articleTitle }}主题文化</h2>
      <div class="secondLevel">
          <h3>分类：{{ formValues.typeCN }}</h3>
          <h3>发布人：{{ formValues.createPeople }}</h3>
          <h3>发布时间：{{ formValues.createTime }}</h3>  
      </div>
      <div class="content" v-html="replacrImg(formValues.content)">
      </div>
      <!-- <img class="img" :src="formValues.articleCover" alt="" style="width:100%;height:auto"> -->
      <el-form-item class="form-footer">
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>
   
  </common-dialog>
</template>

<script>
import {details} from "@/api/culExploration/index";
export default {
  name: 'detailExploration',
  components: {
   
  },
  data() {
    return {
      formValues:{
        articleTitle: '',
        typeCN:'',
        content:'',
        createPeople:'',
        createTime:'',
        articleCover:''
      },
      
      
      
    }
  },
  mounted() {
   
  },
  methods: {
    replacrImg(detailText) {
        detailText = detailText.replace(/<img[^>]*>/gi, function(match, capture) {
            return match.replace(/(style="(.*?)")|(width="(.*?)")|(height="(.*?)")/ig,
                'style="max-width:100%;height:auto;"') // 替换style
        });
        return detailText;
    },
    quxiao(){
      this.$refs.dialog.dialogVisible = false
      this.formValues.articleTitle = ''
      this.formValues.typeCN = ''
      this.formValues.content = ''
      this.formValues.createPeople = ''
      this.formValues.createTime = ''
      this.formValues.articleCover = ''
    },
    showDialog(id) {
      this.$refs.dialog.dialogVisible = true
      let params = {
        infoId:id
      }
      details(params).then((res)=>{
        this.formValues = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title{
  display: flex;
  justify-content: center;
}
.secondLevel{
  display: flex;
  justify-content: space-around;
}
.img{
  width: 100%;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  height: 700px !important;
  overflow-y: auto !important;
}
::v-deep .el-button--primary{
  display: none !important;
}
</style>
 