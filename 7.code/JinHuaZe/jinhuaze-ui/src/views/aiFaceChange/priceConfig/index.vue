<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="场景" prop="scene">
        <el-select v-model="queryParams.scene" placeholder="请选择场景" clearable>
          <el-option label="大唐不夜" value="1" />
          <el-option label="汉服体验" value="2" />
          <el-option label="古风摄影" value="3" />
          <el-option label="宫廷风" value="4" />
          <el-option label="民国风" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="服饰" prop="costume">
        <el-select v-model="queryParams.costume" placeholder="请选择服饰" clearable>
          <el-option label="步摇惊棠" value="1" />
          <el-option label="襦裙套装" value="2" />
          <el-option label="凤冠霞帔" value="3" />
          <el-option label="龙袍" value="4" />
          <el-option label="旗袍" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker v-model="operateTimes" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 250px" @change="handleOperateTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        <el-button type="primary" plain icon="el-icon-download" size="mini" @click="handleExport">批量导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column label="序号" align="center" type="index" width="60"></el-table-column>
      <el-table-column prop="name" label="名称" align="center"></el-table-column>
      <el-table-column prop="priceTypeName" label="价格类型" align="center"></el-table-column>
      <el-table-column prop="sceneName" label="场景" align="center"></el-table-column>
      <el-table-column prop="costumeName" label="服饰" align="center"></el-table-column>
      <el-table-column prop="price" label="价格" align="center">
        <template #default="{row}">
          <span class="price-text">¥{{ row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="操作人" align="center"></el-table-column>
      <el-table-column prop="operateTime" label="操作时间" align="center"></el-table-column>
      <el-table-column prop="statusName" label="状态" align="center">
        <template #default="{row}">
          <el-tag :type="row.status === '1' ? 'success' : 'danger'">
            {{ row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template #default="{row}">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(row)">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-close" @click="handleDisable(row)"
            :disabled="row.status === '0'">失效</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 详情弹窗 -->
    <detail-dialog ref="detailDialog" />

    <!-- 新增/编辑弹窗 -->
    <add-edit-dialog ref="addEditDialog" @refresh="getList" />
  </div>
</template>

<script>
import { getAiFaceChangePriceList, disableAiFaceChangePrice } from "@/api/aiFaceChange";
import DetailDialog from "./components/detailDialog.vue";
import AddEditDialog from "./components/addEditDialog.vue";

export default {
  name: "AiFaceChangePriceConfig",
  components: {
    DetailDialog,
    AddEditDialog
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        name: '',
        scene: '',
        costume: '',
        status: '',
        operateTimeStart: '',
        operateTimeEnd: '',
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [
        {
          id: 1,
          name: '默认价格',
          priceType: '0',
          priceTypeName: '默认价格',
          scene: '',
          sceneName: '',
          costume: '',
          costumeName: '',
          price: '3.00',
          operatorName: '管理员',
          operateTime: '2025-07-03 11:15:28',
          status: '1',
          statusName: '启用'
        },
        {
          id: 2,
          name: '大唐不夜-步摇惊棠套餐',
          priceType: '1',
          priceTypeName: '基础套餐',
          scene: '1',
          sceneName: '大唐不夜',
          costume: '1',
          costumeName: '步摇惊棠',
          price: '2.00',
          operatorName: '管理员',
          operateTime: '2025-07-07 09:56:48',
          status: '1',
          statusName: '启用'
        },
        {
          id: 3,
          name: '汉服体验-襦裙套装',
          priceType: '2',
          priceTypeName: '高级套餐',
          scene: '2',
          sceneName: '汉服体验',
          costume: '2',
          costumeName: '襦裙套装',
          price: '5.00',
          operatorName: '张三',
          operateTime: '2025-07-06 14:30:20',
          status: '1',
          statusName: '启用'
        },
        {
          id: 4,
          name: '古风摄影-凤冠霞帔',
          priceType: '3',
          priceTypeName: '豪华套餐',
          scene: '3',
          sceneName: '古风摄影',
          costume: '3',
          costumeName: '凤冠霞帔',
          price: '8.00',
          operatorName: '李四',
          operateTime: '2025-07-05 16:45:12',
          status: '0',
          statusName: '禁用'
        },
        {
          id: 5,
          name: '宫廷风-龙袍',
          priceType: '3',
          priceTypeName: '豪华套餐',
          scene: '4',
          sceneName: '宫廷风',
          costume: '4',
          costumeName: '龙袍',
          price: '12.00',
          operatorName: '王五',
          operateTime: '2025-07-04 10:20:35',
          status: '1',
          statusName: '启用'
        },
        {
          id: 6,
          name: '民国风-旗袍',
          priceType: '2',
          priceTypeName: '高级套餐',
          scene: '5',
          sceneName: '民国风',
          costume: '5',
          costumeName: '旗袍',
          price: '6.50',
          operatorName: '赵六',
          operateTime: '2025-07-03 11:15:28',
          status: '1',
          statusName: '启用'
        }
      ],
      total: 6,
      loading: false,
      operateTimes: []
    }
  },
  mounted() {
    // 注释掉API调用，使用假数据
    // this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        // 使用假数据，实际项目中取消注释下面的代码
        // const { rows, total } = await getAiFaceChangePriceList(this.queryParams)
        // this.tableData = rows
        // this.total = total
      } finally {
        this.loading = false
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.operateTimes = []
      this.queryParams.operateTimeStart = ''
      this.queryParams.operateTimeEnd = ''
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleOperateTimeChange(times) {
      if (times && times.length === 2) {
        this.queryParams.operateTimeStart = times[0] + ' 00:00:00'
        this.queryParams.operateTimeEnd = times[1] + ' 23:59:59'
      } else {
        this.queryParams.operateTimeStart = ''
        this.queryParams.operateTimeEnd = ''
      }
    },
    handleAdd() {
      // 只有单一价格可以新增
      this.$refs.addEditDialog.open(null, 'single')
    },
    handleEdit(row) {
      // 根据价格类型判断编辑模式
      const mode = row.priceType === '0' ? 'default' : 'single'
      this.$refs.addEditDialog.open(row, mode)
    },
    handleDetail(row) {
      this.$refs.detailDialog.open(row)
    },
    async handleDisable(row) {
      try {
        await this.$confirm('失效后记录不可恢复，请谨慎操作！', '失效', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 实际项目中取消注释下面的代码
        // const { code, msg } = await disableAiFaceChangePrice({ id: row.id })
        // if (code === 200) {
        //   this.$message.success(msg || '操作成功')
        //   this.getList()
        // }
        
        // 模拟操作成功
        row.status = '0'
        row.statusName = '禁用'
        this.$message.success('操作成功')
      } catch (error) {
        console.log('取消操作')
      }
    },
    handleExport() {
      this.download('/ai-face-change/price/export', {
        ...this.queryParams
      }, `AI换脸价格配置_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped lang="scss">
.price-text {
  color: #E6A23C;
  font-weight: 600;
}
</style>