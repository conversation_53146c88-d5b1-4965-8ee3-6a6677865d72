<script>
import AddShowroom from "./components/addShowroom.vue";
import { getListDetails, getManageDetails } from "@/api/ticketPriceReview";
import { getExamineUserList } from "@/api/common";

export default {
    components: {
        AddShowroom
    },
    name: "addDialog",
    data () {
        return {
            // baseUrl,
            detailsFrom: null,
            imgUrl: process.env.VUE_APP_FILE_URL,
            visible: false,
            form: {
                roomName: '',
                isWork: 2,
                name: '',
                price: '',
                period: '',
                refund: '',
                num: '',
                child: '',
                remark: ''
            },

            activities: [
                {
                    title: '发起申请',
                    list: [],
                    createTime: '',
                    avatar: 'https://tvax2.sinaimg.cn/large/650694a1ly1hq7q12owg8j20zk0qon2e.jpg'
                }, {
                    title: '审批人',
                    list: [],
                    createTime: '',
                    avatar: 'https://tvax2.sinaimg.cn/large/650694a1ly1hq7q12owg8j20zk0qon2e.jpg'
                }, {
                    title: '最终审批人',
                    list: [],
                    createTime: '',
                    avatar: 'https://tvax2.sinaimg.cn/large/650694a1ly1hq6nt7vsr9j20xc0p040p.jpg'
                }],
            loading: false,
            imgList: [],
            detailsId: '',//详情id
            rejectRemark: '',//驳回原因
        };
    },
    watch: {},
    created () {
        this.form = JSON.parse(this.$route.query.item);
        this.detailsFrom = this.$route.query.from;
        this.getExamineUserList();
    },
    methods: {
        open () {
            this.visible = true;
        },
        async getExamineUserList () {
            const { data } = await getExamineUserList({
                createBy: this.form.createBy
            });
            this.activities[0].list = data.currentUserList;
            this.activities[0].createTime = data.currentUserList[0].createTime;
            this.activities[1].list = data.operateList;
            this.activities[2].list = data.manageList;
        },
        // 返回列表
        goBack () {
            this.$router.go(-1);
        },

        // 获取跳页参数
        getParams (value) {
            this.saveForm(false);
            this.rejectRemark = value;
        },
        // 保存表单
        saveForm (value) {
            let params = {};
            if (value) {
                params = {
                    state: '1',
                    id: this.form.id,
                };
            } else {
                params = {
                    state: '2',
                    id: this.form.id,
                    rejectRemark: this.rejectRemark
                };
            }
            const fn = this.detailsFrom === 'manage' ? getManageDetails : getListDetails;
            console.log(params);
            fn(params).then((res) => {
                if (res.data && value) {
                    this.$message({
                        type: 'success',
                        message: '审核已通过!'
                    });
                    this.handleClose();
                } else if (res.data && !value) {
                    this.$message({
                        type: 'success',
                        message: '已驳回!'
                    });
                    this.handleClose();
                } else {
                    this.$message({
                        type: 'error',
                        message: '操作失败!'
                    });
                }
            });
        },
        // 返回
        handleClose () {
            this.$router.go(-1);
        },
        // 驳回
        reject () {
            this.$refs.add.showDialog();
        },

    }
}
</script>

<template>
    <div class="content">
        <div class="content-body">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div class="left-title">
                        <div class="left-icon"></div>
                        <span>审批详情</span>
                    </div>
                    <el-button type="warning" class="title-rightbtn" plain icon="el-icon-close" size="mini"
                        @click="handleClose">返回
                    </el-button>
                </div>
                <div class="card-body">
                    <el-descriptions :title="form.showroomName" :column="2">
                        <el-descriptions-item label="门票名称">{{ form.admissionName }}</el-descriptions-item>
                        <el-descriptions-item label="门票价格">{{ form.price }}</el-descriptions-item>
                        <el-descriptions-item label="使用期限">{{ form.serviceLifeName }}</el-descriptions-item>
                        <el-descriptions-item label="退票规则">{{ form.refundRulesName }}</el-descriptions-item>
                        <el-descriptions-item label="参观人数">{{ form.visitCount }}</el-descriptions-item>
                        <el-descriptions-item label="儿童人数">{{ form.childrenCount }}</el-descriptions-item>
                        <el-descriptions-item label="门票备注">{{ form.admissionRemark }}</el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-card>
            <el-card class="box-card" style="margin-top:10px">
                <div slot="header" class="clearfix">
                    <div class="left-title">
                        <div class="left-icon"></div>
                        <span>审批流程</span>
                    </div>
                </div>
                <div class="card-body">
                    <el-timeline>
                        <el-timeline-item v-for="(activity, index) in activities" :key="index" type="primary"
                            :hide-timestamp="true" :timestamp="activity.createTime">
                            <div class="top-content">
                                <div class="mb8">{{ activity.title }} {{index === 0 ? form.createTime : ''}} </div>
                            </div>
                            <div class="user-list mb8">
                                <div class="user-item" v-for="(user, index) in activity.list" :key="index">
                                    <el-avatar :src="imgUrl + user.avatar" icon="el-icon-user-solid"></el-avatar>
                                    <div>{{ user.nickName }}</div>
                                </div>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                </div>
            </el-card>
            <div class="bottom-temp">
                <el-button class="bottom-btn" type="primary" size="small" @click="saveForm(true)">审批通过
                </el-button>
                <el-button class="bottom-btn" type="primary" plain size="small" @click="reject(false)">驳回</el-button>
            </div>
        </div>

        <AddShowroom :rejectRemark.sync="rejectRemark" @saveForm="getParams" ref="add" />
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
    width: 100% !important;
}

::v-deep .el-card {
    border-radius: 10px;
    border: none;
}

::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 15vh !important;
}

::v-deep .el-form-item__content {
    margin-left: 0 !important;
}

.left-title {
    display: flex;
    align-items: center;
}

.clearfix {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.title-rightbtn {
    position: absolute;
    right: 0px;
}

.bottom-temp {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.bottom-btn {
    display: flex;
    justify-content: center;
}

.top-content {
    display: flex;
    align-items: center;
}

.avatar-uploader {
    height: 130px;

    ::v-deep .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}

.avatar-uploader ::v-deep .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
}

.img-box {
    display: flex;
    gap: 10px;
}

.content-body {
    margin: 15px;

    .second-title {
        margin-left: 20px;
        margin-bottom: 20px;
        margin-right: 10px;
    }

    .box-card {
        display: flex;
        flex-direction: column;
        position: relative;

        .bottom-btn {
            position: absolute;
            bottom: 10px;
            left: 50%;
            right: 50%;
            width: 60px;
        }

        .clearfix {
            display: flex;
            align-items: center;

            span {
                font-weight: bold !important;
            }

            .left-icon {
                width: 3px;
                height: 16px;
                margin-right: 4px;
                margin-top: 2px;
                border-radius: 10px;
                background-color: rgb(0, 121, 254);
            }
        }
    }

    .title-btn {
        display: flex;
        align-items: flex-start;
        line-height: 34px;
    }

    .item-card {
        margin-bottom: 10px;
        margin-left: 20px;
        margin-right: 20px;
        position: relative;

        .delete {
            position: absolute;
            top: 0;
            right: 10px;
        }
    }
}

.user-list {
    display: flex;
    gap: 20px;
    text-align: center;
    line-height: 16px;
}
</style>
