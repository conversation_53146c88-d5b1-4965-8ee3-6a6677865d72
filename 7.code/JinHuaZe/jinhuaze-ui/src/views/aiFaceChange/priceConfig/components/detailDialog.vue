<template>
  <common-dialog title="详情" :visible.sync="visible" width="500px" :is-view="true">
    <div class="detail-container">

      <!-- 价格信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">价格信息</div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">名称：</span>
            <span class="value">{{ details.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">价格类型：</span>
            <span class="value">{{ details.priceTypeName }}</span>
          </div>
          <!-- 动态显示场景字段 -->
          <div class="info-item" v-if="details.priceType !== '0'">
            <span class="label">场景：</span>
            <span class="value">{{ details.sceneName }}</span>
          </div>
          <!-- 动态显示服饰字段 -->
          <div class="info-item" v-if="details.priceType !== '0'">
            <span class="label">服饰：</span>
            <span class="value">{{ details.costumeName }}</span>
          </div>
          <div class="info-item">
            <span class="label">价格：</span>
            <span class="value amount">{{ details.price }}元</span>
          </div>
        </div>
      </div>

      <!-- 其他信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <div class="section-title">其他信息</div>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">操作人：</span>
            <span class="value">{{ details.operatorName }}</span>
          </div>
          <div class="info-item">
            <span class="label">操作时间：</span>
            <span class="value">{{ details.operateTime }}</span>
          </div>
        </div>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { getAiFaceChangePriceDetail } from "@/api/aiFaceChange";

export default {
  name: "PriceDetailDialog",
  data() {
    return {
      visible: false,
      details: {
        name: '',
        priceType: '',
        priceTypeName: '',
        sceneName: '',
        costumeName: '',
        price: '',
        status: '',
        statusName: '',
        operatorName: '',
        operateTime: ''
      }
    }
  },
  methods: {
    async open(row) {
      this.visible = true
      if (row && row.id) {
        try {
          // 实际项目中取消注释下面的代码
          // const { data } = await getAiFaceChangePriceDetail({ id: row.id })
          // this.details = data
          
          // 使用传入的数据
          this.details = { ...row }
        } catch (error) {
          console.error('获取详情失败:', error)
        }
      } else {
        // 如果没有传入row.id，直接使用传入的row数据
        this.details = { ...row }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  position: relative;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.info-section {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  padding: 8px 0 8px 12px;
  border-left: 3px solid #409EFF;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 6px;
  margin: 0 -20px 16px -20px;
  position: relative;
}

.status-tag {
  padding: 6px 10px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &.status-enabled {
    background: #67C23A;
    color: white;
  }
  
  &.status-disabled {
    background: #F56C6C;
    color: white;
  }
}

.info-content {
  padding: 0 4px;
  background: #fafafa;
  border-radius: 4px;
  padding: 12px 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  min-height: 20px;
  line-height: 1.4;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .label {
    font-size: 14px;
    color: #606266;
    min-width: 85px;
    flex-shrink: 0;
    font-weight: 400;
    padding-top: 1px;
  }
  
  .value {
    font-size: 14px;
    color: #303133;
    flex: 1;
    word-break: break-all;
    padding-top: 1px;
    
    &.amount {
      color: #E6A23C;
      font-weight: 600;
      font-size: 16px;
    }
  }
}

// 覆盖弹窗样式
::v-deep .el-dialog__body {
  padding: 20px !important;
}
</style>
