<script>
import { getList } from "@/api/refundOrderReview";

export default {
  name: "index",
  data() {
    return {
      pageFromManage: false,
      showSearch: true,
      queryParams: {
        refundSn: '',
        orderSn: '',
        refundApplyContactsName: '',
        refundApplyTimeStart: '',
        refundApplyTimeEnd: '',
        pageNum: 1,
        pageSize: 10,

        date: []
      },
      tableData: [],
      total: 0,
      loading: false
    };
  },
  mounted() {
    this.pageFromManage = this.$route.query.type === 'manage'
    this.init();
  },
  activated() {
    this.init();
  },
  methods: {
    // 查看详情
    goDetails(row) {
      this.$router.push({path: 'refundDetails', query: { orderSn: row.orderSn, isManage: this.pageFromManage ? 1: 0 }});
    },
    async init() {
      const obj = { ...this.queryParams }
      if (obj.date.length) {
        obj.refundApplyTimeStart = obj.date[0] + ' 00:00:00'
        obj.refundApplyTimeEnd = obj.date[1] + ' 23:59:59'
        delete obj.date
      }
      const { data } = await getList(obj, this.pageFromManage);
      this.tableData = data.rows
      this.total = data.total
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.init()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.init()
    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="退款单号" prop="refundSn">
        <el-input v-model="queryParams.refundSn" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="关联销售单号" prop="orderSn">
        <el-input v-model="queryParams.orderSn" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="申请人" prop="refundApplyContactsName">
        <el-input v-model="queryParams.refundApplyContactsName" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="申请退款时间" prop="date">
        <el-date-picker
          v-model="queryParams.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="refundSn" label="退款单号"></el-table-column>
      <el-table-column prop="refundApplyTime" label="申请退款时间"></el-table-column>
      <el-table-column prop="refundAmount" label="申请退款金额"></el-table-column>
      <el-table-column prop="orderSourceText" label="订单来源"></el-table-column>
      <el-table-column prop="totalAmount" label="原订单金额（元）"></el-table-column>
      <el-table-column prop="refundApplyContactsName" label="退款申请人"></el-table-column>
      <el-table-column prop="orderSn" label="关联销售订单号"></el-table-column>
      <el-table-column prop="name" label="操作" width="100">
        <template #default="{row}">
          <el-button @click="goDetails(row)" size="mini" type="text" icon="el-icon-view">审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="init"/>
  </div>
</template>

<style scoped lang="scss">
</style>
