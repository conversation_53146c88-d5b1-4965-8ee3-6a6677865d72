<template>
  <common-dialog 
    :title="title" 
    :visible.sync="visible" 
    width="600px" 
    @confirm="handleSubmit" 
    @cancel="handleCancel">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="名称：" prop="name">
            <el-input v-model="form.name" placeholder="请输入" maxlength="50" />
          </el-form-item>
        </el-col>
        
        <!-- 场景字段 - 只在单一价格模式下显示 -->
        <el-col :span="24" v-if="priceMode === 'single'">
          <el-form-item label="场景：" prop="scene">
            <el-select v-model="form.scene" placeholder="请选择" style="width: 100%">
              <el-option label="大唐不夜" value="1" />
              <el-option label="汉服体验" value="2" />
              <el-option label="古风摄影" value="3" />
              <el-option label="宫廷风" value="4" />
              <el-option label="民国风" value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <!-- 服饰字段 - 只在单一价格模式下显示 -->
        <el-col :span="24" v-if="priceMode === 'single'">
          <el-form-item label="服饰：" prop="costume">
            <el-select v-model="form.costume" placeholder="请选择" style="width: 100%">
              <el-option label="步摇惊棠" value="1" />
              <el-option label="襦裙套装" value="2" />
              <el-option label="凤冠霞帔" value="3" />
              <el-option label="龙袍" value="4" />
              <el-option label="旗袍" value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="24">
          <el-form-item label="价格：" prop="price">
            <el-input 
              v-model.number="form.price" 
              placeholder="请输入">
              <template #append>元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </common-dialog>
</template>

<script>
import { addAiFaceChangePrice, updateAiFaceChangePrice, getAiFaceChangePriceDetail } from "@/api/aiFaceChange";

export default {
  name: "AddEditDialog",
  data() {
    return {
      visible: false,
      title: '',
      isEdit: false,
      priceMode: 'single', // 'default' 或 'single'
      form: {
        id: null,
        name: '',
        scene: '',
        costume: '',
        price: null
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        scene: [
          { required: true, message: '请选择场景', trigger: 'blur' }
        ],
        costume: [
          { required: true, message: '请选择服饰', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async open(row, mode = 'single') {
      this.visible = true;
      this.priceMode = mode; // 设置模式
      this.isEdit = !!row;
      this.title = this.isEdit ? '编辑价格配置' : '新增价格配置';
      
      // 根据模式调整验证规则
      if (this.priceMode === 'default') {
        // 默认价格模式不需要场景和服饰的验证
        delete this.rules.scene;
        delete this.rules.costume;

        this.rules.name = [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ];
      } else {
        // 单一价格模式需要场景和服饰的验证
        this.rules.scene = [{ required: true, message: '请选择场景', trigger: 'blur' }];
        this.rules.costume = [{ required: true, message: '请选择服饰', trigger: 'blur' }];

        this.rules.name = [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ];
      }
      
      if (this.isEdit && row.id) {
        try {
          // 实际项目中取消注释下面的代码
          // const { data } = await getAiFaceChangePriceDetail({ id: row.id });
          // this.form = { ...data };
          
          // 使用传入的数据
          this.form = {
            id: row.id,
            name: row.name,
            scene: row.scene,
            costume: row.costume,
            price: parseFloat(row.price)
          };
        } catch (error) {
          console.error('获取详情失败:', error);
        }
      } else {
        this.resetForm();
      }
    },
    
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            let result;
            if (this.isEdit) {
              // 实际项目中取消注释下面的代码
              // result = await updateAiFaceChangePrice(this.form);
              
              // 模拟编辑成功
              result = { code: 200, msg: '编辑成功' };
            } else {
              // 实际项目中取消注释下面的代码
              // result = await addAiFaceChangePrice(this.form);
              
              // 模拟新增成功
              result = { code: 200, msg: '新增成功' };
            }
            
            if (result.code === 200) {
              this.$message.success(result.msg || '操作成功');
              this.visible = false;
              this.$emit('refresh');
            }
          } catch (error) {
            console.error('操作失败:', error);
          }
        }
      });
    },
    
    handleCancel() {
      // 确保 visible 被设置为 false 以关闭弹窗
      this.visible = false;
      this.resetForm();
    },
    
    resetForm() {
      this.form = {
        id: null,
        name: '',
        scene: '',
        costume: '',
        price: null
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-input-number {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>