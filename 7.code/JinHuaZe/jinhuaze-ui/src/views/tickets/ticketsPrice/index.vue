<script>
import AddDialog from "./addDialog.vue";
import { getTicketPriceList, delTicketPrice } from "@/api/tickets";

export default {
  name: "index",
  components: { AddDialog },
  data() {
    return {
      showSearch: true,
      queryParams: {
        condition: '',
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      total: 0,
      loading: false
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const { rows, total } = await getTicketPriceList(this.queryParams)
      this.tableData = rows
      this.total = total
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.getList()
    },
    onAdd() {
      this.$refs.addDialog.open()
    },
    delRow({ id }) {
      this.$confirm('确定要删除该价格吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code, msg } = await delTicketPrice({ id })
        if (code === 200) {
          this.$message.success(msg || '删除成功')
          await this.getList()
        }
      }).catch(() => {
      });
    }

  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="关键字" prop="condition">
        <el-input v-model="queryParams.condition" placeholder="请输入门票名称" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          v-hasPermi="['system:menu:add']"
          @click="onAdd"
        >新增门票价格</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%">
      <el-table-column prop="showroomName" label="展厅名称"></el-table-column>
      <el-table-column prop="admissionName" label="门票名称" width="200"></el-table-column>
      <el-table-column prop="price" label="门票价格"></el-table-column>
      <el-table-column prop="serviceLifeName" label="使用期限" width="150"></el-table-column>
      <el-table-column prop="refundRulesName" label="退票规则"></el-table-column>
      <el-table-column prop="admissionRemark" label="门票备注" show-overflow-tooltip></el-table-column>
      <el-table-column prop="statusName" label="审批状态"></el-table-column>
      <el-table-column prop="currentUserName" label="当前处理人" width="120"></el-table-column>
      <el-table-column prop="rejectRemark" label="驳回原因" show-overflow-tooltip></el-table-column>
<!--      <el-table-column prop="name" label="是否为工作票"></el-table-column>-->
      <el-table-column label="操作" width="170">
        <template #default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="$refs.addDialog.open(row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="delRow(row)"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="$refs.addDialog.open(row, true)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add-dialog ref="addDialog" @refresh="handleQuery" />
  </div>
</template>

<style scoped lang="scss">

</style>
