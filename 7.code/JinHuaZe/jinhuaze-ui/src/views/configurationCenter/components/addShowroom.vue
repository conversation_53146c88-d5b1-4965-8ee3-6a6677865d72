<template>
  <common-dialog ref="dialog" width="61%" title="新增公告" class="blueDialogScroll">
    <el-form class="forms" :model="formValues" :rules="rules" ref="form" size="small" label-width="110px">
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input v-model="formValues.noticeTitle" placeholder="请输入" maxlength="50" ></el-input>
          <!-- onkeyup="value=value.replace(/[^\u4E00-\u9FA5]/g,'')"  @blur="formValues.noticeTitle = $event.target.value" -->
        </el-form-item>
        <el-form-item label="公告内容" prop="noticeContent">
          <my-editor v-model="formValues.noticeContent" />
        </el-form-item>
      <el-form-item class="form-footer">
        <el-button type="primary" @click="submitForm('form')" :disabled="loading" :loading="loading">
          {{ loading ? "提交中..." : "发布公告" }}
        </el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>

  </common-dialog>
</template>

<script>
import { add } from "@/api/configurationCenter/index";
import MyEditor from "@/components/WangEditor"
export default {
  name: 'addShowroom',
  components: {
    MyEditor
  },
  data() {
    return {
      loading:false,
      formValues: {
        noticeTitle: '',
        noticeContent:'',
      },
      rules: {
        noticeTitle: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        noticeContent: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      }
      
    }
  },
  mounted() {
   
  },
  methods: {
    quxiao(){
      this.$refs.dialog.dialogVisible = false
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const res = await add(this.formValues)
            const { code } = res
            if (code === 200) {

              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.$emit('success')
              this.loading = false
              this.$refs.dialog.dialogVisible = false
            }
          } catch {
            this.loading = false
          }
        } else {
          return false;
        }
      });
    },
    showDialog() {
      this.$refs.dialog.dialogVisible = true
      for(var key in this.formValues){
          this.formValues[key] = ''
      }
    }
    
  }
}
</script>

<style lang="scss" scoped>


.mb{
  margin-bottom: 40px;
}
.mt{
  margin-top: -40px;
}

//行颜色
::v-deep .el-select-dropdown__item {
  color: #606266 !important;
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
::v-deep .el-dialog__body {
  overflow-y: auto !important;
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}

//上传图片
::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  ::v-deep .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  ::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  ::v-deep .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
</style>
