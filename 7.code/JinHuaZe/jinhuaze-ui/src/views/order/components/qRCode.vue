<template>
  <common-dialog ref="dialog" width="40%" title="门票详情" class="xx">
    <el-form class="forms"  ref="form" size="small" label-width="110px">
      <div class="canvan" ref="canvasWrapper"></div>
      <el-form-item class="form-footer">
        <el-button v-if="down==true" type="primary" @click="downLoadQrcode">下载</el-button>
        <el-button @click="quxiao">返回</el-button>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
import QRCode from 'qrcode'
export default {
  name: 'qRCode',
  components: {
  },
  data() {
    return {
      text:'777777',
      down:false
    }
  },
  mounted() {
   
  },
  methods: {
    //返回
    quxiao(){
      this.$refs.dialog.dialogVisible = false
    },
    downLoadQrcode() {
      var canvasData = this.$refs.canvasWrapper.getElementsByTagName("canvas");//获取并转换
      var a = document.createElement("a");
      var event = new MouseEvent("click"); // 创建一个单击事件
      a.href = canvasData[0].toDataURL("image/png");
      a.download = "drcQrcode";
      a.dispatchEvent(event); // 触发a的单击事件
    },
    showDialog(val,url) {
      let that = this
      if(url=='addOrder'){
         that.down = true
      }else if(url=='orderDetails'){
         that.down = false
      }
      that.text = val
      this.show = true
      setTimeout(()=>{
        QRCode.toCanvas(this.text, {
          width: 200,
          margin: 1
        }, (error, canvas) => {
          if (error) {
            return
          }
          // 在canvas的父元素中插入canvas元素
          that.$refs.canvasWrapper.appendChild(canvas)
        })
      },0)
      this.$refs.dialog.dialogVisible = true
    } 
  }
}
</script>

<style lang="scss" scoped>


.canvan{
  display: flex;
  justify-content: center;
}

//弹窗高度
::v-deep .el-dialog {
  height:auto !important;
}

//弹窗主体
.xx{
  ::v-deep .el-dialog__body {
  height: auto !important;
  overflow-y: auto !important;
}
}

//底部按钮
.form-footer {
  width: 100%;
  height: 65px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  margin-bottom: 0;

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    margin-top: 15px;
  }
}
</style>
