<script>
import AddDialogT from "./createDialog.vue";
import AddDialog from "./createDialogs.vue";
import { getTicketPriceList, delTicketPrice } from "@/api/tickets";
import { orderList} from "@/api/tickets/index";
export default {
  name: "index",
  components: { AddDialog,AddDialogT },
  data() {
    return {
      showSearch: true,
      queryParams: {
        condition: '',
        pageNum: 1,
        pageSize: 10,
        startTime:'',
        endTime:''
      },
      tableData: [],
      total: 0,
      loading: false,
      times:[]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    timeValue(v){
      this.queryParams.startTime = v[0]
      this.queryParams.endTime = v[1]
    },
    async getList() {
      const { rows, total } = await orderList(this.queryParams)
      this.tableData = rows
      this.total = total
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.queryParams.startTime = ''
      this.queryParams.endTime = ''
      this.times = []
      this.getList()
    },
    onAdd() {
      this.$refs.addDialog.open()
    },
    delRow({ id }) {
      this.$confirm('确定要删除该价格吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code, msg } = await delTicketPrice({ id })
        if (code === 200) {
          this.$message.success(msg || '删除成功')
          await this.getList()
        }
      }).catch(() => {
      });
    }

  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="关键字" prop="condition">
        <el-input v-model="queryParams.condition" placeholder="请输入创建人" />
      </el-form-item>
      <el-form-item label="创建时间" prop="condition">
        <el-date-picker
          v-model="times"
          type="daterange"
          value-format="yyyy-MM-dd"
          @change="timeValue"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="2">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          v-hasPermi="['system:menu:add']"
          @click="onAdd"
        >生成工作票</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%">
      <el-table-column prop="batchCode" label="批次号"></el-table-column>
      <el-table-column prop="name" label="门票名称" ></el-table-column>
      <el-table-column prop="amount" label="门票数量"></el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createName" label="创建人" ></el-table-column>
      <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
      <!--      <el-table-column prop="name" label="是否为工作票"></el-table-column>-->
      <el-table-column label="操作" width="80">
        <template #default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="$refs.addDialogT.open(row, true)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <add-dialog ref="addDialog" @refresh="handleQuery" />
    <add-dialogT ref="addDialogT" @refresh="handleQuery" />
  </div>
</template>

<style scoped lang="scss">

</style>
