<script>
import { orderSave } from "@/api/tickets";
export default {
  name: "addDialog",
  components: {},
  data() {
    return {
      visible: false,
      isView: false,
      form: {
        id: null,
        name:'辽博数字展厅-工作票',
        amount:'',
        remark:''
      },
      rules: {
        name: [{required: true, message: '请输入名称'}],
        amount: [{required: true, message: '请选择数量'}],
        remark: [{required: true, message: '请填写备注'}]
      }
    }
  },
  computed: {
    title() {
      return this.isView ? '门票价格详情' : this.form.id ? '编辑门票价格' : '生成工作票'
    }
  },
  mounted() {

  },
  methods: {
    open() {
      this.form.name = "辽博数字展厅-工作票"
      this.form.amount = "1"
      this.form.remark = ""
      this.visible = true
    },
    quxiao(){
      this.visible = false
    },
    onSubmit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const {code, msg} = await orderSave(this.form, !!this.form.id)
          if (code === 200) {
            this.$message.success(msg || '操作成功')
            this.visible = false
            this.$emit('refresh')
          }
        }
      })
    }
  }
}
</script>

<template>
  <common-dialog :title="title" :visible.sync="visible" width="1200px">

    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small" :disabled="isView">
      <el-row>
        <el-col :span="24">
          <el-form-item label="门票名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="门票数量" prop="amount">
            <el-input-number v-model="form.amount"  :min="1" label="描述文字"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" :rows="6" placeholder="请输入" :maxlength="200"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item style="text-align: center">
        <el-button type="primary" @click="onSubmit">提交订单</el-button>
        <el-button @click="quxiao">取消</el-button>
      </el-form-item>
    </el-form>
    <div slot="footer"></div>
  </common-dialog>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100% !important;
}
.area-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
p {
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
