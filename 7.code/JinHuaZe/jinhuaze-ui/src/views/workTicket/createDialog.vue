<script>
import { importExcelZip, workTicketDetails } from "@/api/tickets";
import QRCodes from "@/views/order/components/qRCode.vue";
import { downFile } from '@/utils/down'

export default {
  name: "addDialog",
  components: {QRCodes},
  data() {
    return {
      id:'',
      visible: false,
      isView: false,
      details: {},
      tableData: []
    }
  },
  computed: {
    title() {
      return '工作票详情'
    }
  },
  mounted() {

  },
  methods: {
    open(row) {
      this.details = row
      this.visible = true
      this.init()
    },
    async init() {
      const { data } = await workTicketDetails({ id: this.details.id })
      this.tableData = data
    },
    async onload() {
      try {
        let params = {
          id:this.details.id
        };
        const { data, headers } = await importExcelZip(params);
        downFile(data, headers);
      } catch {
      }
    }
  }
}
</script>

<template>
  <common-dialog title="详情" :visible.sync="visible" width="1200px">

    <el-form ref="form" label-width="120px" size="small" :disabled="isView">
      <!-- <div class="area-title">{{ title }} <el-button type="primary" size="small" @click="$refs.qRCodes.showDialog('abcdefg','addOrder')">下载工作票</el-button></div> -->
      <div class="area-title">{{ title }} <el-button type="primary" size="small" @click="onload">下载工作票</el-button></div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="批次号:" prop="showroomId">
            {{ details.batchCode }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="门票名称:" prop="admissionName">
            <el-tooltip class="item" effect="dark" :content="details.name"
                        placement="top-start">
              <p>{{ details.name }}</p>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类别:" prop="admissionName">
            门票
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="门票价格（元）:" prop="price">
            0.00
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="门票数量:" prop="serviceLife">
           {{ details.amount }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建人:" prop="price">
            {{ details.createName }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建时间:" prop="visitCount">
            {{ details.createTime }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:" prop="childrenCount">
            {{ details.remark }}
          </el-form-item>
        </el-col>
      </el-row>


      <div class="area-title">操作记录</div>
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column type="index" label="序号" ></el-table-column>
        <el-table-column prop="createTime" label="操作时间" ></el-table-column>
        <el-table-column prop="createName" label="操作人"></el-table-column>
        <el-table-column prop="details" label="操作详情"></el-table-column>
      </el-table>
    </el-form>
    <div slot="footer"></div>

    <QRCodes ref="qRCodes" />
  </common-dialog>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100% !important;
}
.area-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
p {
  margin: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
