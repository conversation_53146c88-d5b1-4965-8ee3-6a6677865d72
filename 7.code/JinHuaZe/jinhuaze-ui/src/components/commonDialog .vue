<template>
    <el-dialog
      custom-class="dialog blueDialog"
      :key="key"
      :width="width"
      :visible.sync="dialogVisible"
      v-if="this.dialogVisible"
      :append-to-body="true"
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :show-close="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <template #title>
        <div class="title-content">
          <div class="title-content__title">{{ title }}</div>
          <div class="title-content__btns">
            <i class="el-icon-close" @click="closeDialog"></i>
          </div>
        </div>
      </template>
      <slot></slot>
      <div slot="footer" class="dialog-footer">
        <slot name="footer">
          <template v-if="!isView">
            <el-button type="primary" size="small" @click="onConfirm">{{ confirmButtonText }}</el-button>
            <el-button size="small" @click="onCancel">取消</el-button>
          </template>
          <el-button v-else size="small" @click="onCancel">关闭</el-button>
        </slot>
      </div>
    </el-dialog>
  </template>

  <script>
  export default {
    name: 'commonDialog',
    props: {
      width: {
        type: String,
        default: '30%'
      },
      title: {
        type: String,
        require: true
      },
      isCloseCallback: {
        type: Boolean,
        default: false
      },
      visible: Boolean,
      confirmButtonText: {
        type: String,
        default: '保存'
      },
      isView: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        fullscreen: false,
        dialogVisible: this.visible,
        key: "key" + Math.random() * 100000000
      }
    },
    watch: {
      dialogVisible(val) {
        this.$emit('dialog-visible', val)
        this.$emit('update:visible', val)
      },
      visible(val) {
        this.dialogVisible = val
      }
    },
    methods: {
      full(){
        this.fullscreen = !this.fullscreen
        console.log(this.fullscreen,'this.fullscreen');
      },
      handleClose() {
        if (this.isCloseCallback) {
          this.$parent.quxiao();
        }

      },
      closeDialog() {
        if (this.isCloseCallback) {
          this.$parent.quxiao();
        }
        this.$emit('cancel')
        this.dialogVisible = false
      },
      onConfirm() {
        this.$emit("confirm")
      },
      onCancel() {
        this.$emit("update:visible", false)
        this.$emit("cancel")
      }
    }
  }
  </script>

  <style lang="scss" scoped>
  $mainBg: #0065b0;
  $height: 70px;
  $blue_color: #164a84;
  $fontColor: #333544;
  ::v-deep .el-dialog__header,
  ::v-deep .el-dialog__footer {
    padding: 0;
  }

  .title-content {
    background-color: #f6f7fc;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #97a4ba;

    &__title {
      text-indent: 16px;
      font-size: 16px;
    }

    &__btns {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 18px;
      padding-right: 16px;

      i {
        cursor: pointer;
      }
    }
  }

  ::v-deep .el-dialog__body {
    // height: 50vh;
    // overflow: scroll;;
    padding: 20px;
  }

  .dialog-footer {
     padding: 10px 0;
    display: flex;
    justify-content: center;
  }

  ::v-deep .blueDialog {
    min-width: 550px !important;

    .el-dialog__header {
      width: 100%;
      min-height: 50px;
      background-color: $fontColor;

      .title-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        background-color: $fontColor;
      }

      .title-content__title {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #fff;
        font-weight: bolder;

        &::before {
          display: inline-block;
          content: '';
          width: 20px;
          height: 24px;
          margin-right: 20px;
          background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAYCAYAAAD6S912AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA4FpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAyIDc5LjE2NDQ2MCwgMjAyMC8wNS8xMi0xNjowNDoxNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpmMDU4ZWExNS0yMjcyLTM2NDktYTQ2YS01ZDM1YzJhMDZlZDUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDA3QjY5NThDRjYyMTFFQzkxQ0FDQTk2Q0YwMThDNUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDA3QjY5NTdDRjYyMTFFQzkxQ0FDQTk2Q0YwMThDNUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDIxLjIgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6M2FkOWQzNjQtMzczYy00MjQ0LTk1ODktZTFmZDUxZDgwYTRlIiBzdFJlZjpkb2N1bWVudElEPSJhZG9iZTpkb2NpZDpwaG90b3Nob3A6YmQ5YTkyZTQtZjlhZC0zZDQ3LTlkMTUtZWFiNzE3OTE4NWRmIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+3r3yoAAAAKpJREFUeNpijKz7/58BO2Bc1shAMmBioDIYgQaykKrh1DXauLAMiJmpaaAFEK8HYh5qGXgaiH2B+BAQS1HLQBAwBOKTQGxADQNhOUwGiA8DsSclBn4E4ltIfFBYbgbinIFPh1DAD8RqSPwvQBwBxFvJNdAEVBpB2U+gMX6BEheaQWmQId5A/IzSWDaFRoItsmGUGAhKe4HQsKNKpHRSLZbNtEZLbFobCBBgAEEuHLtdFmr9AAAAAElFTkSuQmCC');
        }
      }

      .close {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 30px;
        cursor: pointer;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA4FpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAyIDc5LjE2NDQ2MCwgMjAyMC8wNS8xMi0xNjowNDoxNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpmMDU4ZWExNS0yMjcyLTM2NDktYTQ2YS01ZDM1YzJhMDZlZDUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDA3QjY5NUNDRjYyMTFFQzkxQ0FDQTk2Q0YwMThDNUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDA3QjY5NUJDRjYyMTFFQzkxQ0FDQTk2Q0YwMThDNUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDIxLjIgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6M2FkOWQzNjQtMzczYy00MjQ0LTk1ODktZTFmZDUxZDgwYTRlIiBzdFJlZjpkb2N1bWVudElEPSJhZG9iZTpkb2NpZDpwaG90b3Nob3A6YmQ5YTkyZTQtZjlhZC0zZDQ3LTlkMTUtZWFiNzE3OTE4NWRmIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+aXsy4gAAAMxJREFUeNqMk7sNwjAQhg+LAUhBlggbRBSkAykrMgA1Urq4gLABS5ACNoD/Ils6Tn6d9Ff29+n8uNX5Yg0RHZEJ+VBZbZAWGTx8RW7ItgDmPXfHnFjwQJ7IDhkzEl6zSOOYiQVvpCuQaPjArHGLLyWxShKCZ14wYpOUNEIShbnWqk0vGYWEYnBIEJJQDNZHkPUt/A9BQa3OrO8kKahF677tLiUxGXhOvM6fIAbnnngRVBk4JamMm6ocHJO0/A8GpHcTVjLOLNn7cf4JMAAtmE2X+zL/IwAAAABJRU5ErkJggg==');

      }

    }

    .el-dialog__body {
      //min-height: 845px;
      max-height: 1000px;
      overflow-y: hidden;

      /*.el-table__body-wrapper {
        overflow: auto;
        height: 240px !important;
      }*/
    }

    /*.dialog-footer {

      button:not(.el-button--text) {
        min-width: 64px;
        height: 32px;
        //line-height: 32px;
        font-size: 14px;
        font-weight: bold;
        background-color: #f7f8fa;
        padding: 0 10px;
        margin-right: 10px;

        &.el-button--primary {
          border: 1px solid $blue_color;
          background-color: $blue_color;
        }
      }
    }*/

    .dialogBtn {
      float: right;
      margin-right: 0;

      button {
        width: 80px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: bold;
        background-color: #f7f8fa;
        padding: 0;

        &.el-button--primary {
          border: 1px solid $blue_color;
          background-color: $blue_color;
        }
      }
    }
  }

  </style>

  <style lang="scss" scoped>
  .blueDialogScroll {
    overflow: hidden !important;

    .el-dialog__body {
      max-height: 600px !important;
      overflow-y: auto !important;

    }
  }
  </style>
