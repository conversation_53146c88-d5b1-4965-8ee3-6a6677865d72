<script>
import { getDetails, addOrUpdate } from "@/api/playNotice/index";

export default {
  name: "addDialog",
  data() {
    return {
      // baseUrl,
      visible: false,
      form: {
        openDescriptionList: [{
          daterange: []
        }],
      },
      rules: {
        roomName: [{ required: true, message: '请选择展厅' }],
        isWork: [{ required: true, message: '请选择是否为工作票' }],
        name: [{ required: true, message: '请输入门票名称' }],
        price: [{ required: true, message: '请输入门票价格' }],
        period: [{ required: true, message: '请选择使用期限' }],
        refund: [{ required: true, message: '请选择退票规则' }],

        child: [{ required: true, message: '请输入儿童人数' }],
        remark: [{ required: true, message: '请输入门票备注' }],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 22,
      loading: false,
      daterange: []

    };

  },
  watch: {
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 添加开放说明
    addTable() {
      let obj = {
        date: '',
        specify: '',
        daterange: [],
        startTime: '',
        endTime: ''
      };
      this.form.openDescriptionList.push(obj);
    },
    open() {
      this.visible = true;
    },
    // 获取列表
    getList() {
      getDetails(this.queryParams).then(res => {
        if (res.code == 200) {
          this.$nextTick(() => {
            this.form = res.data;
            this.form.openDescriptionList.forEach((item, index) => {
              this.$set(this.form.openDescriptionList, index, {
                ... this.form.openDescriptionList[index],
                daterange: [item.startTime, item.endTime]
              })
            });
            this.total = res.total;
          })
        }
      });
    },
    // 返回列表
    goBack() {
      this.$router.go(-1);
    },
    //  改变多选框
    handleSelectionChange(value) {
      console.log(value, './value');
    },
    // 添加文化banner图
    addLine() {

    },
    // 删除改行
    deleteLine(row) {
      if (this.form.openDescriptionList.length == 1) {
        this.$message.error('已删除到最后一项');
      } else {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.form.openDescriptionList = this.form.openDescriptionList.filter(item => item !== row);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      }
    },
    // 改变时间
    changeDate(value, row) {
      if (value) {
        let daterange = value;
        row.startTime = daterange[0] || '';
        row.endTime = daterange[1] || '';
        console.log(this.form.openDescriptionList);
      }
    },
    // 通过/驳回
    saveForm() {
      let params = {
        ...this.form,
        id: '',
      };
      if (this.form.openDescriptionList.some(item => item.daterange.length == 0)) {
        this.$message({
          type: 'error',
          message: '时间不得为空'
        });
      } else {
        addOrUpdate(params).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功'
            });
            this.getList();
          } else {
            this.$message({
              type: 'error',
              message: '操作失败'
            });
          }
        });
      }

    },
  }
}
</script>
<template>
  <div class="content">
    <div class="content-body">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <div class="left-icon"></div>
          <span>游玩须知维护</span>
        </div>
        <div class="card-body">
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="开放说明" prop="img">
                  <el-button class="edit_btn" @click="addTable" type="text" icon="el-icon-plus">添加开放说明</el-button>
                  <el-table v-loading="loading" :data="form.openDescriptionList" stripe style="width: 100%" border
                    ref="multipleTable" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column label="序号" type="index" width="50">
                    </el-table-column>
                    <el-table-column prop="startTime" label="开放时间">
                      <template #default="{ row }">
                        <el-date-picker @change="changeDate($event, row)" value-format="yyyy-MM-dd" v-model="row.daterange
                          " type="daterange" range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                      </template>
                    </el-table-column>
                    <el-table-column prop="specify" label="具体说明">
                      <template #default="{ row }">
                        <el-input v-model="row.specify" maxlength="30" placeholder='请输入'></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="操作" width="120">
                      <template #default="{ row }">
                        <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteLine(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="优待政策" prop="ipreferentialPoliciesmg">
                  <el-input v-model="form.preferentialPolicies" maxlength="200" :show-word-limit="false" type="textarea"
                    placeholder='请输入' :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="交通信息" prop="trafficInformation">
                  <el-input v-model="form.trafficInformation" maxlength="200" :show-word-limit="false" type="textarea"
                    placeholder='请输入' :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="停车场" prop="parkingLot">
                  <el-input v-model="form.parkingLot" maxlength="200" :show-word-limit="false" type="textarea"
                    placeholder='请输入' :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="温馨提示" prop="tips">
                  <el-input v-model="form.tips" maxlength="200" :show-word-limit="false" type="textarea"
                    placeholder='请输入' :rows="5"></el-input>
                </el-form-item>
                <el-form-item label="景点咨询热线" prop="tel">
                  <el-input v-model="form.tel" placeholder='请输入'></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <el-button class="bottom-btn" type="primary" size="small" @click="saveForm">保存</el-button>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
  width: 100% !important;
}

::v-deep .el-card {
  border-radius: 10px;
  border: none;
}

.bottom-btn {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.card-body {
  margin-bottom: 40px;
}

.content-body {
  margin: 15px;

  .second-title {
    margin-left: 20px;
    margin-bottom: 20px;
    margin-right: 10px;
  }

  .box-card {
    display: flex;
    flex-direction: column;
    position: relative;

    .bottom-btn {
      position: absolute;
      bottom: 10px;
      left: 50%;
      right: 50%;
      width: 60px;
    }

    .clearfix {
      display: flex;
      align-items: center;

      span {
        font-weight: bold !important;
      }

      .left-icon {
        width: 3px;
        height: 16px;
        margin-right: 4px;
        margin-top: 2px;
        border-radius: 10px;
        background-color: rgb(0, 121, 254);
      }
    }
  }

  .title-btn {
    display: flex;
    align-items: flex-start;
    line-height: 34px;
  }

  .item-card {
    margin-bottom: 10px;
    margin-left: 20px;
    margin-right: 20px;
    position: relative;

    .delete {
      position: absolute;
      top: 0;
      right: 10px;
    }
  }
}
</style>
