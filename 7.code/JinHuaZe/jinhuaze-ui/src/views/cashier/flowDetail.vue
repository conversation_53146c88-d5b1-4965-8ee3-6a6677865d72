<script>

export default {
    name: "flowDetail",
    data () {
        return {
            form: {
                roomName: '',
                isWork: 2,
                name: '',
                price: '',
                period: '',
                refund: '',
                num: '',
                child: '',
                remark: ''
            },
            loading: false,
        };
    },
    watch: {},
    created () {
        this.form = JSON.parse(this.$route.query.item);
    },
    methods: {
        // 返回
        handleClose () {
            this.$router.go(-1);
        },

    }
}
</script>

<template>
    <div class="content">
        <div class="content-body">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <div class="left-title">
                        <div class="left-icon"></div>
                        <span>流水详情</span>
                    </div>
                    <el-button type="warning" class="title-rightbtn" plain icon="el-icon-close" size="mini"
                        @click="handleClose">返回
                    </el-button>
                </div>
                <div class="card-body">
                    <el-descriptions :title="form.showroomName" :column="4">
                        <el-descriptions-item label="流水号">{{ form.turnoverSn }}</el-descriptions-item>
                        <el-descriptions-item label="支付方式">{{ form.payType == 2 ? '现金支付' : '扫码支付' }}</el-descriptions-item>
                        <el-descriptions-item label="购票时间">{{ form.createTime }}</el-descriptions-item>
                        <el-descriptions-item label="购票收银员">{{ form.nickName }}</el-descriptions-item>
                        <el-descriptions-item label="退票时间">{{ form.refundTime }}</el-descriptions-item>
                        <el-descriptions-item label="退票收银员">{{ form.refundNickName }}</el-descriptions-item>
                        <el-descriptions-item label="应收金额">{{ form.totalAmount }}</el-descriptions-item>
                        <el-descriptions-item label="退款金额">{{ form.refundAmount }}</el-descriptions-item>
                        <el-descriptions-item label="实收金额">{{ form.actualAmount }}</el-descriptions-item>
                        <el-descriptions-item label="退款状态">{{ form.refundStatus == 0 ? '未退款' : '退款成功' }}</el-descriptions-item>
                      </el-descriptions>
                </div>
            </el-card>
            <el-card class="box-card bottom-temp">
              <el-table :data="form.orderList" stripe style="width: 100%">
                <el-table-column prop="orderSn" label="流水号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="admissionName" label="门票名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="admissionPrice" label="门票价格（元）" show-overflow-tooltip></el-table-column>
                <el-table-column prop="verifincationSn" label="二维码编号" show-overflow-tooltip></el-table-column>
              </el-table>
            </el-card>
        </div>
    </div>
</template>

<style scoped lang="scss">
::v-deep .el-select {
    width: 100% !important;
}

::v-deep .el-card {
    border-radius: 10px;
    border: none;
}

::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 15vh !important;
}

::v-deep .el-form-item__content {
    margin-left: 0 !important;
}

.left-title {
    display: flex;
    align-items: center;
}

.clearfix {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.title-rightbtn {
    position: absolute;
    right: 0px;
}

.bottom-temp {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.bottom-btn {
    display: flex;
    justify-content: center;
}

.top-content {
    display: flex;
    align-items: center;
}


.content-body {
    margin: 15px;

    .second-title {
        margin-left: 20px;
        margin-bottom: 20px;
        margin-right: 10px;
    }

    .box-card {
        display: flex;
        flex-direction: column;
        position: relative;

        .bottom-btn {
            position: absolute;
            bottom: 10px;
            left: 50%;
            right: 50%;
            width: 60px;
        }

        .clearfix {
            display: flex;
            align-items: center;

            span {
                font-weight: bold !important;
            }

            .left-icon {
                width: 3px;
                height: 16px;
                margin-right: 4px;
                margin-top: 2px;
                border-radius: 10px;
                background-color: rgb(0, 121, 254);
            }
        }
    }

    .title-btn {
        display: flex;
        align-items: flex-start;
        line-height: 34px;
    }

    .item-card {
        margin-bottom: 10px;
        margin-left: 20px;
        margin-right: 20px;
        position: relative;

        .delete {
            position: absolute;
            top: 0;
            right: 10px;
        }
    }
}

</style>
