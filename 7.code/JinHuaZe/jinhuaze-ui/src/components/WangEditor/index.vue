<template>
  <div class="my-editor-container" :class="{disabled: isDisabled}">
    <div style="border: 1px solid #DCDFE6; margin-top: 10px">
      <!-- 工具栏 -->
      <Toolbar
        style="border-bottom: 1px solid #DCDFE6"
        :editor="editor"
        :defaultConfig="toolbarConfig"
      />
      <!-- 编辑器 -->
      <Editor
        style="height: 400px; overflow-y: hidden"
        :defaultConfig="editorConfig"
        v-model="html"
        @onChange="onChange"
        @onCreated="onCreated"
        @onBlur="onBlur"
      />
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import emitter from 'element-ui/src/mixins/emitter';
import { getToken } from "@/utils/auth";
// import { baseUrl } from "@/config/env";

export default {
  name: "MyEditor",
  components: { Editor, Toolbar },
  mixins: [emitter],
  props: {
    value: String,
    disabled: Boolean,
    notAllowedFile: Boolean
  },
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  data() {
    const server = process.env.VUE_APP_BASE_API + '/museum-main/oss/upload'
    return {
      editor: null,
      html: `${this.value}`,
      // html: `<p>${this.value}</p>`, //带空格
      toolbarConfig: {
        // toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
        excludeKeys: this.notAllowedFile ? [ 'group-image', 'group-video' /* 隐藏哪些菜单 */ ] : [],
      },
      editorConfig: {
        placeholder: "请输入内容...",
        readOnly: this.isDisabled,
        autoFocus: false,

        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          uploadImage: {
            server,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024,
            headers: {
              Authorization: 'Bearer ' + getToken()
            },
            // 自定义插入图片
            customInsert(res, insertFn) {
              console.log('customInsert', res)
              insertFn(process.env.VUE_APP_FILE_URL + res.data)
            }
          },
          uploadVideo: {
            server,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024,
            headers: {
              Authorization: 'Bearer ' + getToken()
            },
            // 自定义插入视频
            customInsert(res, insertFn) {
              insertFn(process.env.VUE_APP_FILE_URL + res.data)
            }
          }
        },
      },
    };
  },
  computed: {
    isDisabled() {
      return this.disabled || (this.elForm || {}).disabled
    }
  },
  watch: {
    value(val) {
      if (val !== this.editor.getHtml()) {
        const context = `${val}`
        // const context = `<p>${val}</p>` //带空格
        this.editor.setHtml(context);
      }
      this.dispatch('ElFormItem', 'el.form.change', [val]);
    },
    isDisabled: {
      handler(val) {
        if (val) {
          this.editor && this.editor.disable()
        } else {
          this.editor && this.editor.enable()
        }
      },
      immediate: false,
      deep: true
    }
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 【注意】一定要用 Object.seal() 否则会报错
    },
    onChange(editor) {
      const htmlVal = editor.getHtml()
      const val = htmlVal === '<p><br></p>' ? '' : htmlVal
      if (this.value !== htmlVal) {
        this.$emit('input', val)
      }
    },
    onBlur() {
      this.dispatch('ElFormItem', 'el.form.blur', [this.value]);
    }
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁 editor ，重要！！！
  },
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
.my-editor-container {
  > div {
    border-radius: 4px;
    overflow: hidden;
  }
  &.disabled {
    > div {
      border-color: #dfe4ed!important;
      > div:first-child {
        border-color: #dfe4ed!important;
      }
    }
  }
  ::v-deep {
    .title {
      margin-bottom: 0;
    }
    .w-e-text-placeholder {
      line-height: 1.3;
    }
  }
  .w-e-full-screen-container {
    z-index: 999;
  }
  &.disabled ::v-deep {
    .w-e-text-container, .w-e-toolbar {
      background: #f5f7fa;
      color: #C0C4CC;
      cursor: not-allowed;
    }
  }
}
</style>
