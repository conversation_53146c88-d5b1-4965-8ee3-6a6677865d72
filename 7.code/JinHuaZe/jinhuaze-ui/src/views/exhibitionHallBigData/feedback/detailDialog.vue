<script>
import { replyFeedback } from "@/api/exhibitionHallBigData/feedback";

export default {
  name: "detailDialog",
  data() {
    return {
      visible: false,
      url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      details: {
        imgList: []
      },
      replyContent: '',
      imgUrl: process.env.VUE_APP_FILE_URL
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.replyContent = ''
        this.details = {
          imgList: []
        }
      }
    }
  },
  methods: {
    open(row) {
      this.details = { ...row }
      this.visible = true
    },
    async onConfirm() {
      if (!this.details.replyContent.trim()) {
        this.$message.warning('请输入回复内容')
        return
      }
      const { code, msg } = await replyFeedback({
        id: this.details.id,
        replyContent: this.details.replyContent
      })
      if (code === 200) {
        this.$message.success(msg)
        this.visible = false
        this.$emit('refresh')
      }
    }
  }
}
</script>

<template>
  <common-dialog title="投诉/建议详情" :visible.sync="visible" width="1200px" append-to-body :is-view="details.state == 1" confirm-button-text="回复投诉" @confirm="onConfirm">
    <div class="feedback-details-container">
      <div class="left">
        <ul>
          <li>反馈单号：{{ details.id }}</li>
          <li>反馈时间：{{ details.createTime }}</li>
          <li>反馈类型：{{ details.typeName }}</li>
          <li>反馈人姓名：{{ details.name }}</li>
          <li>反馈人联系电话：{{ details.phone }}</li>
        </ul>
      </div>
      <div class="right">
        <el-row class="mb20">
          <el-col :span="4" class="label_col">内容描述：</el-col>
          <el-col :span="20">
            <el-card>
              {{ details.content }}
            </el-card>
          </el-col>
        </el-row>
        <el-row class="mb20">
          <el-col :span="4" class="label_col">现场照片：</el-col>
          <el-col :span="20">
            <el-card>
              <div v-if="details.imgList && details.imgList.length > 0" class="img-list">
                <el-image
                  v-for="(img, index) in details.imgList"
                  :key="index"
                  style="width: 100px; height: 100px"
                  :src="imgUrl + img"
                  :preview-src-list="[imgUrl + img]">
                </el-image>
              </div>
              <el-empty v-else description="暂无数据" :image-size="60"></el-empty>
            </el-card>
          </el-col>
        </el-row>
        <el-row class="mb20">
          <el-col :span="4" class="label_col">{{ details.state == 1 ? '回复内容' : '回复投诉/建议'}}：</el-col>
          <el-col :span="20">
            <el-card>
              <!--       每条数据只能回复一次       -->
              <el-input v-model="details.replyContent" :readonly="details.state == 1" type="textarea" placeholder="请输入回复内容" :rows="6" maxlength="200"/>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </common-dialog>
</template>

<style scoped lang="scss">
.feedback-details-container {
  display: flex;
  .left {
    width: 270px;
    //border: 1px solid red;
  }
  .right {
    flex: 1;
    border-left: 1px solid #D7D7D7;
    padding-left: 30px;
  }
  li, ul {
    list-style: none;
    padding: 0;
    margin: 0;
    &+li {
      margin-top: 20px;
    }
  }
  .el-card {
    line-height: 24px;
  }
  .label_col {
    font-weight: bold;
  }
  .img-list {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }
  .el-image {
    display: block;
    border-radius: 5px;
  }
}
</style>
