<script>
import { getListData } from "@/api/cashier";
export default {
  name: "index",
  data() {
    return {
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        turnoverSn: '',
        dataArry: [],
        refundStatus:null
      },
      refundStatusList: [{
        label:'未退款',
        value:0
      },
      {
        label:'退款成功',
        value:1
      }],
      tableData: [],
      total: 0,
      loading: false
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 查看详情
    goDetails(row) {
      let query = {
        item: JSON.stringify(row)
      };
      console.log(query, '.传参');
      this.$router.push({ path: 'flowDetail', query: query });
    },
    getList() {
      getListData(this.queryParams).then(res => {
        this.tableData = res.rows;
        this.total = res.total;
        // this.loading = false;
      }
      );
    },
    handleQuery() {
      this.queryParams.startTime = this.queryParams.dataArry[0];
      this.queryParams.endTime = this.queryParams.dataArry[1]
      this.getList();
    },
    /** 重置 */
    resetQuery() {
      for (var key in this.queryParams) {
        this.queryParams[key] = '';
      }
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getList();
    },
    handleSelectionChange() {

    },
    onAdd() {
      this.$refs.addDialog.open();
    },
    handleExport(){
      this.download('museum-main/turnover/export', {
        ...this.queryParams
      }, `log_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="流水号" prop="turnoverSn">
        <el-input v-model="queryParams.turnoverSn" placeholder="请输入流水号" />
      </el-form-item>
      <el-form-item label="购票时间" prop="type">
        <el-date-picker v-model="queryParams.dataArry" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="退款状态" prop="refundStatus">
        <el-select v-model="queryParams.refundStatus" placeholder="请选择">
          <el-option v-for="item in refundStatusList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column prop="turnoverSn" label="流水号" show-overflow-tooltip></el-table-column>
      <el-table-column prop="payType" label="支付方式">
        <template slot-scope="scope">
          <div>{{ scope.row.payType == 2 ? '现金支付' : '扫码支付' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="应收金额"></el-table-column>
      <el-table-column prop="actualAmount" label="实收金额"></el-table-column>
      <el-table-column prop="refundAmount" label="退款金额"></el-table-column>
      <el-table-column prop="contactsName" label="联系人"></el-table-column>
      <el-table-column prop="contactsPhone" label="手机号码" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="购票时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="nickName" label="购票收银员"></el-table-column>
      <el-table-column prop="refundTime" label="退票时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="refundNickName" label="退票收银员"></el-table-column>
      <el-table-column prop="refundStatus" label="退款状态">
        <template slot-scope="scope">
          <div>{{ scope.row.refundStatus == 0 ? '未退款' : '退款成功' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="refundReason" label="退款原因"></el-table-column>
      <el-table-column prop="name" label="操作">
        <template slot-scope="scope">
          <el-button @click="goDetails(scope.row)" size="mini" type="text">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<style scoped lang="scss">
// ::v-deep .el-input--small .el-input__inner {
//   width: 280px;
// }
</style>
