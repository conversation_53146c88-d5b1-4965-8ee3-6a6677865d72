<script>
import DetailDialog from "./detailDialog.vue";
import { getFeedBackList, getFeedbackStatistics, delFeedback } from "@/api/exhibitionHallBigData/feedback";

export default {
  name: "index",
  components: { DetailDialog },
  data() {
    return {
      showSearch: true,
      queryParams: {
        condition: '',
        type: '',
        state: '',
        pageNum: 1,
        pageSize: 10,
      },
      statisticsData: [
        {
          title: '全部',
          type: '',
          state: '',
          num: '0'
        },
        {
          title: '待处理投诉',
          type: '2',
          state: '0',
          num: '0'
        },
        {
          title: '已处理投诉',
          type: '2',
          state: '1',
          num: '0'
        },
        {
          title: '待处理建议',
          type: '1',
          state: '0',
          num: '0'
        },
        {
          title: '已处理建议',
          type: '1',
          state: '1',
          num: '0'
        }
      ],
      statisticsIndex: 0,
      tableData: [],
      total: 0,
      loading: false
    }
  },
  mounted() {
    this.getStatistics()
    this.getList()
  },
  methods: {
    async getStatistics() {
      const { data } = await getFeedbackStatistics(this.queryParams)
      this.statisticsData[0].num = data.qb || 0
      this.statisticsData[1].num = data.dclTs || 0
      this.statisticsData[2].num = data.yclTs || 0
      this.statisticsData[3].num = data.dclJy || 0
      this.statisticsData[4].num = data.yclJy || 0
    },
    async getList() {
      const { rows, total } = await getFeedBackList(this.queryParams)
      this.total = total
      this.tableData = rows.map(item => {
        item.contentSub = (item.content || '').substring(0, 20)
        return item
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
      this.getStatistics()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.getList()
      this.getStatistics()
    },
    onCardClick({ type, state }, index) {
      this.queryParams.type = type
      this.queryParams.state = state
      this.statisticsIndex = index
      this.handleQuery()
    },
    viewDetail(row) {
      this.$refs.detailDialog.open(row)
    },
    delRow({ id }) {
      this.$confirm('确定要删除该反馈意见吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code, msg } = await delFeedback({ id })
        if (code === 200) {
          this.$message.success(msg)
          await this.getList()
          await this.getStatistics()
        }
      }).catch(() => {
      });
    }

  }
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="关键字" prop="condition">
        <el-input v-model="queryParams.condition" placeholder="请输入反馈人姓名、联系电话、内容" style="width: 300px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="statistics-container">
      <el-card v-for="(item, index) in statisticsData" :class="{active: index === statisticsIndex}" :key="index" @click.native="onCardClick(item, index)">
        <p>{{ item.title }}</p>
        <h2>{{ item.num }}</h2>
      </el-card>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%">
      <el-table-column prop="id" label="反馈单号"></el-table-column>
      <el-table-column prop="createTime" label="反馈时间"></el-table-column>
      <el-table-column prop="typeName" label="反馈类型"></el-table-column>
      <el-table-column prop="startName" label="处理状态"></el-table-column>
      <el-table-column prop="contentSub" label="反馈内容"></el-table-column>
      <el-table-column prop="name" label="操作" width="120">
        <template #default="{row}">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="viewDetail(row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="delRow(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <detail-dialog ref="detailDialog" @refresh="getList" />
  </div>
</template>

<style scoped lang="scss">
.statistics-container {
  display: flex;
  gap: 20px;
  padding-bottom: 20px;
  > div {
    flex: 1;
    text-align: center;
    cursor: pointer;
    &.active {
      border-color: #0079FE;
      background: #EDF2FD;
    }
  }
  p {
    color: #AAAAAA;
  }
  h2 {
    margin: 0;
  }
}
</style>
